import { useState, useEffect, useCallback, useMemo } from 'react'
import type { GraphNode, GraphEdge } from '../types'

interface LODConfig {
  // 缩放级别阈值
  zoomThresholds: {
    minimal: number    // 最小细节级别
    reduced: number    // 减少细节级别
    normal: number     // 正常细节级别
    detailed: number   // 详细级别
  }
  // 节点数量阈值
  nodeCountThresholds: {
    small: number      // 小图谱
    medium: number     // 中等图谱
    large: number      // 大图谱
    huge: number       // 超大图谱
  }
}

interface LODLevel {
  name: 'minimal' | 'reduced' | 'normal' | 'detailed'
  showNodeLabels: boolean
  showEdgeLabels: boolean
  showNodeProperties: boolean
  showNodeIcons: boolean
  nodeRadius: number
  edgeWidth: number
  labelFontSize: number
  maxVisibleNodes: number
  maxVisibleEdges: number
  simplifyGeometry: boolean
}

interface LODState {
  currentLevel: LODLevel
  zoomLevel: number
  nodeCount: number
  shouldUpdate: boolean
}

const DEFAULT_CONFIG: LODConfig = {
  zoomThresholds: {
    minimal: 0.3,
    reduced: 0.6,
    normal: 1.0,
    detailed: 2.0
  },
  nodeCountThresholds: {
    small: 100,
    medium: 500,
    large: 1000,
    huge: 2000
  }
}

const LOD_LEVELS: Record<string, LODLevel> = {
  minimal: {
    name: 'minimal',
    showNodeLabels: false,
    showEdgeLabels: false,
    showNodeProperties: false,
    showNodeIcons: false,
    nodeRadius: 8,
    edgeWidth: 1,
    labelFontSize: 10,
    maxVisibleNodes: 2000,
    maxVisibleEdges: 3000,
    simplifyGeometry: true
  },
  reduced: {
    name: 'reduced',
    showNodeLabels: false,
    showEdgeLabels: false,
    showNodeProperties: false,
    showNodeIcons: true,
    nodeRadius: 12,
    edgeWidth: 1.5,
    labelFontSize: 11,
    maxVisibleNodes: 1500,
    maxVisibleEdges: 2000,
    simplifyGeometry: true
  },
  normal: {
    name: 'normal',
    showNodeLabels: true,
    showEdgeLabels: false,
    showNodeProperties: false,
    showNodeIcons: true,
    nodeRadius: 16,
    edgeWidth: 2,
    labelFontSize: 12,
    maxVisibleNodes: 1000,
    maxVisibleEdges: 1500,
    simplifyGeometry: false
  },
  detailed: {
    name: 'detailed',
    showNodeLabels: true,
    showEdgeLabels: true,
    showNodeProperties: true,
    showNodeIcons: true,
    nodeRadius: 20,
    edgeWidth: 2.5,
    labelFontSize: 14,
    maxVisibleNodes: 500,
    maxVisibleEdges: 800,
    simplifyGeometry: false
  }
}

export const useLevelOfDetail = (
  nodes: GraphNode[],
  edges: GraphEdge[],
  zoomTransform: { k: number } | null,
  config: Partial<LODConfig> = {}
) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  const [lodState, setLodState] = useState<LODState>({
    currentLevel: LOD_LEVELS.normal,
    zoomLevel: 1.0,
    nodeCount: 0,
    shouldUpdate: false
  })

  // 根据缩放级别和节点数量计算LOD级别
  const calculateLODLevel = useCallback((zoom: number, nodeCount: number): LODLevel => {
    // 首先根据节点数量确定基础LOD级别
    let baseLevelName: string
    if (nodeCount <= finalConfig.nodeCountThresholds.small) {
      baseLevelName = 'detailed'
    } else if (nodeCount <= finalConfig.nodeCountThresholds.medium) {
      baseLevelName = 'normal'
    } else if (nodeCount <= finalConfig.nodeCountThresholds.large) {
      baseLevelName = 'reduced'
    } else {
      baseLevelName = 'minimal'
    }

    // 然后根据缩放级别进行调整
    if (zoom >= finalConfig.zoomThresholds.detailed) {
      // 高缩放级别，提升细节
      if (baseLevelName === 'minimal') baseLevelName = 'reduced'
      else if (baseLevelName === 'reduced') baseLevelName = 'normal'
      else if (baseLevelName === 'normal') baseLevelName = 'detailed'
    } else if (zoom <= finalConfig.zoomThresholds.minimal) {
      // 低缩放级别，降低细节
      baseLevelName = 'minimal'
    } else if (zoom <= finalConfig.zoomThresholds.reduced) {
      // 中低缩放级别
      if (baseLevelName === 'detailed') baseLevelName = 'normal'
      else if (baseLevelName === 'normal') baseLevelName = 'reduced'
    }

    return LOD_LEVELS[baseLevelName]
  }, [finalConfig])

  // 过滤可见节点和边
  const getVisibleElements = useCallback((level: LODLevel) => {
    let visibleNodes = nodes
    let visibleEdges = edges

    // 如果节点数量超过LOD级别的最大值，进行采样
    if (nodes.length > level.maxVisibleNodes) {
      // 按重要性排序（连接数、类型权重等）
      const nodesWithImportance = nodes.map(node => {
        const connections = edges.filter(e => e.source === node.id || e.target === node.id).length
        const typeWeight = getNodeTypeWeight(node.type)
        return {
          node,
          importance: connections * typeWeight
        }
      })

      nodesWithImportance.sort((a, b) => b.importance - a.importance)
      visibleNodes = nodesWithImportance
        .slice(0, level.maxVisibleNodes)
        .map(item => item.node)
    }

    // 过滤边，只保留可见节点之间的边
    const visibleNodeIds = new Set(visibleNodes.map(n => n.id))
    visibleEdges = edges.filter(e => 
      visibleNodeIds.has(e.source) && visibleNodeIds.has(e.target)
    )

    // 如果边数量仍然过多，进一步过滤
    if (visibleEdges.length > level.maxVisibleEdges) {
      // 按边的重要性排序（权重、连接重要节点等）
      const edgesWithImportance = visibleEdges.map(edge => {
        const sourceNode = visibleNodes.find(n => n.id === edge.source)
        const targetNode = visibleNodes.find(n => n.id === edge.target)
        const importance = (sourceNode ? getNodeTypeWeight(sourceNode.type) : 1) +
                          (targetNode ? getNodeTypeWeight(targetNode.type) : 1)
        return { edge, importance }
      })

      edgesWithImportance.sort((a, b) => b.importance - a.importance)
      visibleEdges = edgesWithImportance
        .slice(0, level.maxVisibleEdges)
        .map(item => item.edge)
    }

    return { visibleNodes, visibleEdges }
  }, [nodes, edges])

  // 获取节点类型权重
  const getNodeTypeWeight = useCallback((type: string): number => {
    const weights: Record<string, number> = {
      '人物': 1.5,
      '组织': 1.3,
      '地点': 1.1,
      '概念': 1.0,
      '事件': 0.9,
      '产品': 0.8,
      '时间': 0.7
    }
    return weights[type] || 1.0
  }, [])

  // 当缩放或节点数量变化时更新LOD级别
  useEffect(() => {
    const currentZoom = zoomTransform?.k || 1.0
    const currentNodeCount = nodes.length
    
    const newLevel = calculateLODLevel(currentZoom, currentNodeCount)
    
    setLodState(prev => {
      const shouldUpdate = prev.currentLevel.name !== newLevel.name ||
                          Math.abs(prev.zoomLevel - currentZoom) > 0.1 ||
                          prev.nodeCount !== currentNodeCount

      return {
        currentLevel: newLevel,
        zoomLevel: currentZoom,
        nodeCount: currentNodeCount,
        shouldUpdate
      }
    })
  }, [zoomTransform, nodes.length, calculateLODLevel])

  // 获取当前可见的元素
  const visibleElements = useMemo(() => {
    return getVisibleElements(lodState.currentLevel)
  }, [lodState.currentLevel, getVisibleElements])

  // 获取LOD级别描述
  const getLODDescription = useCallback((): string => {
    const level = lodState.currentLevel
    switch (level.name) {
      case 'minimal':
        return '最简模式 - 仅显示节点'
      case 'reduced':
        return '简化模式 - 显示节点和图标'
      case 'normal':
        return '标准模式 - 显示节点标签'
      case 'detailed':
        return '详细模式 - 显示所有信息'
      default:
        return '未知模式'
    }
  }, [lodState.currentLevel])

  return {
    currentLevel: lodState.currentLevel,
    visibleNodes: visibleElements.visibleNodes,
    visibleEdges: visibleElements.visibleEdges,
    shouldUpdate: lodState.shouldUpdate,
    zoomLevel: lodState.zoomLevel,
    description: getLODDescription(),
    config: finalConfig
  }
}
