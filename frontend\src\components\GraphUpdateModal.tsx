import React, { useState } from 'react'
import {
  Modal,
  Upload,
  Button,
  message,
  Progress,
  Typography,
  Space,
  Alert,
  Divider
} from 'antd'
import {
  UploadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { UploadFile, UploadProps } from 'antd'
import { knowledgeGraphApi } from '../services/api'

const { Title, Text } = Typography

interface GraphUpdateModalProps {
  visible: boolean
  onCancel: () => void
  onSuccess: () => void
  graphId: string
  graphName: string
}

interface UpdateResult {
  message: string
  kg_id: string
  total_entities: number
  total_relations: number
  entities_added: number
  relations_added: number
}

const GraphUpdateModal: React.FC<GraphUpdateModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  graphId,
  graphName
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [uploading, setUploading] = useState(false)
  const [updateResult, setUpdateResult] = useState<UpdateResult | null>(null)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState('')
  const [errorDetails, setErrorDetails] = useState<string | null>(null)

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error('请选择要上传的文件')
      return
    }

    const file = fileList[0]
    // 获取实际的文件对象，可能是originFileObj或者file本身
    const actualFile = file.originFileObj || file
    if (!actualFile || !(actualFile instanceof File)) {
      message.error('文件对象无效')
      return
    }

    setUploading(true)
    setProgress(0)
    setUpdateResult(null)
    setErrorDetails(null)
    setCurrentStep('正在上传文件...')

    try {
      // 更详细的进度更新
      const steps = [
        { step: '正在上传文件...', progress: 10 },
        { step: '正在提取文本内容...', progress: 25 },
        { step: '正在分析文档结构...', progress: 40 },
        { step: '正在构建知识图谱...', progress: 60 },
        { step: '正在合并图谱数据...', progress: 80 },
        { step: '正在保存更新结果...', progress: 95 }
      ]

      let stepIndex = 0
      const progressInterval = setInterval(() => {
        if (stepIndex < steps.length) {
          setCurrentStep(steps[stepIndex].step)
          setProgress(steps[stepIndex].progress)
          stepIndex++
        } else {
          clearInterval(progressInterval)
        }
      }, 1000)

      const result = await knowledgeGraphApi.updateGraphWithFile(
        graphId,
        actualFile
      )

      clearInterval(progressInterval)
      setProgress(100)
      setCurrentStep('更新完成！')
      setUpdateResult(result)

      message.success('知识图谱更新成功！')

      // 延迟一下再调用成功回调，让用户看到结果
      setTimeout(() => {
        onSuccess()
      }, 3000)

    } catch (error: any) {
      setProgress(0)
      setCurrentStep('更新失败')
      console.error('更新图谱失败:', error)

      // 提取详细错误信息
      let errorMessage = '更新图谱失败'
      let details = null

      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail
        details = error.response.data.detail
      } else if (error.message) {
        errorMessage = error.message
        details = error.message
      }

      setErrorDetails(details)
      message.error(errorMessage)
    } finally {
      setUploading(false)
    }
  }

  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      // 检查文件类型
      const allowedTypes = [
        'text/plain',
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv'
      ]
      
      if (!allowedTypes.includes(file.type) && !file.name.endsWith('.txt')) {
        message.error('只支持 TXT、PDF、DOCX、DOC、XLS、XLSX、CSV 格式的文件')
        return false
      }

      // 检查文件大小 (50MB)
      if (file.size > 50 * 1024 * 1024) {
        message.error('文件大小不能超过 50MB')
        return false
      }

      // 创建一个包含原始文件对象的UploadFile对象
      const uploadFile: UploadFile = {
        uid: file.uid || Date.now().toString(),
        name: file.name,
        status: 'done',
        originFileObj: file,
        size: file.size,
        type: file.type
      }
      setFileList([uploadFile])
      return false // 阻止自动上传
    },
    fileList,
    onRemove: () => {
      setFileList([])
    },
    maxCount: 1
  }

  const handleCancel = () => {
    if (!uploading) {
      setFileList([])
      setUpdateResult(null)
      setProgress(0)
      setCurrentStep('')
      setErrorDetails(null)
      onCancel()
    }
  }

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          <span>更新知识图谱</span>
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel} disabled={uploading}>
          取消
        </Button>,
        <Button
          key="upload"
          type="primary"
          onClick={handleUpload}
          loading={uploading}
          disabled={fileList.length === 0 || !!updateResult}
          icon={<UploadOutlined />}
        >
          {uploading ? '更新中...' : '开始更新'}
        </Button>
      ]}
      width={600}
      maskClosable={!uploading}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 图谱信息 */}
        <Alert
          message={
            <Space>
              <Text strong>当前图谱:</Text>
              <Text code>{graphName}</Text>
            </Space>
          }
          description="添加新文件将会提取其中的实体和关系，并与现有图谱进行智能合并，使图谱更加丰富和准确。"
          type="info"
          showIcon
        />

        <Divider />

        {/* 文件上传 */}
        <div>
          <Title level={5}>选择要添加的文件</Title>
          <Upload.Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 TXT、PDF、DOCX、DOC、XLS、XLSX、CSV 格式，文件大小不超过 50MB
            </p>
          </Upload.Dragger>
        </div>

        {/* 进度条和状态 */}
        {uploading && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text>{currentStep}</Text>
              <Progress
                percent={progress}
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                处理大文件可能需要较长时间，请耐心等待...
              </Text>
            </Space>
          </div>
        )}

        {/* 错误信息 */}
        {errorDetails && (
          <Alert
            message="更新失败"
            description={
              <div>
                <Text>{errorDetails}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  请检查文件格式是否正确，或尝试使用其他文件。如问题持续存在，请联系技术支持。
                </Text>
              </div>
            }
            type="error"
            showIcon
            closable
            onClose={() => setErrorDetails(null)}
          />
        )}

        {/* 更新结果 */}
        {updateResult && (
          <Alert
            message={
              <Space>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                <Text strong>更新完成！</Text>
              </Space>
            }
            description={
              <Space direction="vertical" size="small">
                <Text>新增实体: {updateResult.entities_added} 个</Text>
                <Text>新增关系: {updateResult.relations_added} 个</Text>
                <Text>总实体数: {updateResult.total_entities} 个</Text>
                <Text>总关系数: {updateResult.total_relations} 个</Text>
              </Space>
            }
            type="success"
            showIcon
          />
        )}

        {/* 注意事项 */}
        <Alert
          message="注意事项"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>系统会自动识别重复实体并进行智能合并</li>
              <li>新文件中的关系会与现有关系进行去重处理</li>
              <li>更新过程可能需要几分钟时间，请耐心等待</li>
              <li>建议上传与现有图谱相关的文档以获得最佳效果</li>
            </ul>
          }
          type="warning"
          showIcon
        />
      </Space>
    </Modal>
  )
}

export default GraphUpdateModal
