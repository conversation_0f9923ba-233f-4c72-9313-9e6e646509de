"""
文件处理API路由
"""
from typing import List
from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse

from app.services.file_service import FileService
from app.services.knowledge_graph_service import KnowledgeGraphService
from app.models.file_models import (
    FileInfo, ProcessResult, UploadResponse,
    FolderScanResult, BatchProcessRequest
)
from loguru import logger

router = APIRouter()
file_service = FileService()
kg_service = KnowledgeGraphService()

@router.post("/upload", response_model=UploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """上传文件"""
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 保存文件
        file_info = await file_service.save_uploaded_file(file_content, file.filename)
        
        return UploadResponse(
            success=True,
            file_info=file_info,
            message="文件上传成功"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@router.post("/process/{file_id}", response_model=ProcessResult)
async def process_file(file_id: str):
    """处理单个文件"""
    try:
        # 查找实际的文件
        from pathlib import Path
        from app.models.file_models import FileType, ProcessStatus

        upload_dir = Path("data/uploads")
        file_info = None

        # 查找匹配的文件
        for file_path in upload_dir.glob(f"{file_id}.*"):
            if file_path.is_file():
                # 根据扩展名确定文件类型
                extension = file_path.suffix.lower()
                file_type_mapping = {
                    '.txt': FileType.TXT,
                    '.pdf': FileType.PDF,
                    '.docx': FileType.DOCX,
                    '.doc': FileType.DOC,
                    '.xlsx': FileType.XLSX,
                    '.xls': FileType.XLS,
                    '.csv': FileType.CSV,
                    '.json': FileType.JSON
                }
                file_type = file_type_mapping.get(extension, FileType.TXT)

                file_info = FileInfo(
                    id=file_id,
                    filename=file_path.name,
                    original_name=file_path.name,
                    file_type=file_type,
                    file_size=file_path.stat().st_size,
                    file_path=str(file_path),
                    status=ProcessStatus.PROCESSING
                )
                break

        if not file_info:
            raise HTTPException(status_code=404, detail=f"文件未找到: {file_id}")
        
        # 提取文本
        text = await file_service.extract_text_from_file(file_info)

        # 使用预处理功能构建知识图谱
        task_id = f"process_{file_id}"
        # 生成基于时间的图谱名称
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_{timestamp}"

        kg = await kg_service.build_knowledge_graph_with_preprocessing(
            text,
            file_info.original_name,
            kg_name,
            task_id=task_id
        )

        # 为了兼容性，仍然生成文本块信息
        chunks = file_service.split_text_into_chunks(text)
        
        # 创建处理结果
        result = ProcessResult(
            file_id=file_id,
            status=ProcessStatus.COMPLETED,
            text_chunks=chunks,
            extracted_entities=[entity.model_dump() for entity in kg.entities],
            extracted_relations=[relation.model_dump() for relation in kg.relations]
        )

        # 成功生成图谱后清理上传文件
        try:
            # 删除当前处理的文件
            await file_service.delete_file(file_info.file_path)
            logger.info(f"单文件处理完成，已删除文件: {file_info.file_path}")
        except Exception as e:
            logger.warning(f"删除文件时出错: {e}")

        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

@router.post("/scan-folder", response_model=FolderScanResult)
async def scan_folder(folder_path: str = Form(...)):
    """扫描文件夹"""
    try:
        result = await file_service.scan_folder(folder_path)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件夹扫描失败: {str(e)}")

@router.post("/batch-process", response_model=List[ProcessResult])
async def batch_process_files(request: BatchProcessRequest):
    """批量处理文件"""
    results = []
    
    for file_id in request.file_ids:
        try:
            # 处理单个文件（复用上面的逻辑）
            result = await process_file(file_id)
            results.append(result)
        except Exception as e:
            # 创建失败结果
            from app.models.file_models import ProcessStatus
            error_result = ProcessResult(
                file_id=file_id,
                status=ProcessStatus.FAILED,
                error_message=str(e)
            )
            results.append(error_result)
    
    return results

@router.post("/batch-process-integrated", response_model=ProcessResult)
async def batch_process_files_integrated(request: BatchProcessRequest):
    """批量处理文件并整合为单一知识图谱"""
    try:
        from pathlib import Path
        from app.models.file_models import FileType, ProcessStatus

        upload_dir = Path("data/uploads")
        texts = []
        filenames = []

        # 收集所有文件的文本内容
        for file_id in request.file_ids:
            # 查找匹配的文件
            file_found = False
            for file_path in upload_dir.glob(f"{file_id}.*"):
                if file_path.is_file():
                    # 根据扩展名确定文件类型
                    extension = file_path.suffix.lower()
                    file_type_mapping = {
                        '.txt': FileType.TXT,
                        '.pdf': FileType.PDF,
                        '.docx': FileType.DOCX,
                        '.doc': FileType.DOC,
                        '.xlsx': FileType.XLSX,
                        '.xls': FileType.XLS,
                        '.csv': FileType.CSV,
                        '.json': FileType.JSON
                    }
                    file_type = file_type_mapping.get(extension, FileType.TXT)

                    file_info = FileInfo(
                        id=file_id,
                        filename=file_path.name,
                        original_name=file_path.name,
                        file_type=file_type,
                        file_size=file_path.stat().st_size,
                        file_path=str(file_path),
                        status=ProcessStatus.PROCESSING
                    )

                    # 提取文本
                    text = await file_service.extract_text_from_file(file_info)
                    texts.append(text)
                    filenames.append(file_path.name)
                    file_found = True
                    break

            if not file_found:
                raise HTTPException(status_code=404, detail=f"文件未找到: {file_id}")

        # 使用新的批量整合方法构建知识图谱
        # 生成基于时间的图谱名称
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        kg_name = f"KG_Batch_{timestamp}"

        kg = await kg_service.build_knowledge_graph_from_multiple_files(
            texts,
            filenames,
            kg_name
        )

        # 创建处理结果
        result = ProcessResult(
            file_id=f"batch_{len(request.file_ids)}_files",
            status=ProcessStatus.COMPLETED,
            text_chunks=[],  # 批量处理时不返回具体的文本块
            extracted_entities=[entity.model_dump() for entity in kg.entities],
            extracted_relations=[relation.model_dump() for relation in kg.relations]
        )

        # 成功生成图谱后清理上传文件
        try:
            deleted_count = await kg_service.cleanup_uploads_after_kg_generation(kg.id)
            logger.info(f"批量处理完成，清理了 {deleted_count} 个文件")
        except Exception as e:
            logger.warning(f"清理文件时出错: {e}")

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量文件整合处理失败: {str(e)}")

@router.get("/supported-formats")
async def get_supported_formats():
    """获取支持的文件格式"""
    from app.core.config import settings
    return {
        "supported_extensions": settings.ALLOWED_EXTENSIONS,
        "max_file_size": settings.MAX_FILE_SIZE,
        "max_file_size_mb": settings.MAX_FILE_SIZE / (1024 * 1024)
    }

@router.post("/cleanup-uploads")
async def cleanup_uploads():
    """手动清理uploads目录中的所有文件"""
    try:
        deleted_count = await file_service.cleanup_uploads_directory()
        return {
            "success": True,
            "deleted_count": deleted_count,
            "message": f"成功清理了 {deleted_count} 个文件"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")
