"""
知识图谱API路由
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, UploadFile, File
from datetime import datetime

from app.services.knowledge_graph_service import KnowledgeGraphService
from app.services.file_service import FileService
from app.models.knowledge_graph import (
    KnowledgeGraph, GraphSearchResult, GraphStats
)
from app.models.file_models import UploadResponse

router = APIRouter()
kg_service = KnowledgeGraphService()

@router.get("/list")
async def list_knowledge_graphs():
    """获取所有知识图谱列表"""
    try:
        graphs = kg_service.list_knowledge_graphs()
        return {"graphs": graphs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图谱列表失败: {str(e)}")

@router.get("/{kg_id}", response_model=KnowledgeGraph)
async def get_knowledge_graph(kg_id: str):
    """获取指定知识图谱"""
    try:
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        return kg
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识图谱失败: {str(e)}")

@router.get("/{kg_id}/stats", response_model=GraphStats)
async def get_graph_stats(kg_id: str):
    """获取图谱统计信息"""
    try:
        # 确保图谱已加载
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        stats = kg_service.get_graph_stats(kg_id)
        if not stats:
            raise HTTPException(status_code=404, detail="无法获取图谱统计信息")
        
        return stats
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/{kg_id}/search", response_model=GraphSearchResult)
async def search_entities(
    kg_id: str, 
    query: str = Query(..., description="搜索关键词"),
    limit: int = Query(10, description="结果数量限制", ge=1, le=100)
):
    """搜索实体"""
    try:
        # 确保图谱已加载
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        result = kg_service.search_entities(kg_id, query, limit)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/{kg_id}/entity/{entity_id}/neighbors", response_model=GraphSearchResult)
async def get_entity_neighbors(
    kg_id: str, 
    entity_id: str,
    depth: int = Query(1, description="搜索深度", ge=1, le=3)
):
    """获取实体的邻居节点"""
    try:
        # 确保图谱已加载
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        result = kg_service.get_entity_neighbors(kg_id, entity_id, depth)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取邻居节点失败: {str(e)}")

@router.get("/{kg_id}/export")
async def export_knowledge_graph(kg_id: str, format: str = Query("json", description="导出格式")):
    """导出知识图谱"""
    try:
        kg = await kg_service.load_knowledge_graph(kg_id)
        if not kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        if format.lower() == "json":
            return kg.dict()
        elif format.lower() == "networkx":
            # 转换为NetworkX格式
            G = kg.to_networkx()
            return kg_service._networkx_to_dict(G)
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@router.delete("/{kg_id}")
async def delete_knowledge_graph(kg_id: str):
    """删除知识图谱"""
    try:
        from pathlib import Path
        from app.core.config import settings
        
        # 删除文件
        file_path = Path(settings.KG_DATA_DIR) / f"{kg_id}.json"
        if file_path.exists():
            file_path.unlink()
        
        # 从内存中移除
        if kg_id in kg_service.graphs:
            del kg_service.graphs[kg_id]
        
        return {"message": "知识图谱删除成功"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.post("/{kg_id}/merge/{other_kg_id}")
async def merge_knowledge_graphs(kg_id: str, other_kg_id: str):
    """合并两个知识图谱"""
    try:
        # 加载两个图谱
        kg1 = await kg_service.load_knowledge_graph(kg_id)
        kg2 = await kg_service.load_knowledge_graph(other_kg_id)
        
        if not kg1 or not kg2:
            raise HTTPException(status_code=404, detail="知识图谱不存在")
        
        # 合并实体和关系
        merged_entities = kg1.entities + kg2.entities
        merged_relations = kg1.relations + kg2.relations
        
        # 去重处理
        merged_entities = kg_service._merge_duplicate_entities(merged_entities)
        
        # 创建新的合并图谱
        import uuid
        merged_kg = KnowledgeGraph(
            id=str(uuid.uuid4()),
            name=f"{kg1.name}_merged_{kg2.name}",
            entities=merged_entities,
            relations=merged_relations,
            metadata={
                "merged_from": [kg_id, other_kg_id],
                "merge_time": str(datetime.now())
            }
        )
        
        # 保存合并后的图谱
        await kg_service.save_knowledge_graph(merged_kg)
        kg_service.graphs[merged_kg.id] = merged_kg
        
        from datetime import datetime

        return {
            "message": "图谱合并成功",
            "merged_kg_id": merged_kg.id,
            "entity_count": len(merged_entities),
            "relation_count": len(merged_relations)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"合并失败: {str(e)}")


@router.post("/{kg_id}/update")
async def update_knowledge_graph_with_file(
    kg_id: str,
    file: UploadFile = File(...)
):
    """向现有知识图谱添加新文件来更新图谱"""
    try:
        # 检查图谱是否存在
        existing_kg = await kg_service.load_knowledge_graph(kg_id)
        if not existing_kg:
            raise HTTPException(status_code=404, detail="知识图谱不存在")

        # 初始化文件服务
        file_service = FileService()

        # 读取并保存上传的文件
        file_content = await file.read()
        file_info = await file_service.save_uploaded_file(file_content, file.filename)

        # 提取文本内容
        text = await file_service.extract_text_from_file(file_info)

        # 从新文件构建临时知识图谱
        temp_kg_name = f"temp_update_{kg_id}"
        temp_kg = await kg_service.build_knowledge_graph_with_preprocessing(
            text,
            file.filename,
            temp_kg_name,
            task_id=f"update_{kg_id}"
        )

        # 合并现有图谱和新图谱
        merged_entities = existing_kg.entities + temp_kg.entities
        merged_relations = existing_kg.relations + temp_kg.relations

        # 去重和消歧处理
        merged_entities = kg_service._merge_duplicate_entities(merged_entities)

        # 更新现有图谱
        existing_kg.entities = merged_entities
        existing_kg.relations = merged_relations
        existing_kg.updated_at = datetime.now()

        # 更新元数据
        if "update_history" not in existing_kg.metadata:
            existing_kg.metadata["update_history"] = []

        existing_kg.metadata["update_history"].append({
            "file_name": file.filename,
            "update_time": str(datetime.now()),
            "entities_added": len(temp_kg.entities),
            "relations_added": len(temp_kg.relations)
        })

        # 保存更新后的图谱
        await kg_service.save_knowledge_graph(existing_kg)
        kg_service.graphs[kg_id] = existing_kg

        # 清理上传的文件
        try:
            await file_service.delete_file(file_info.file_path)
        except Exception as e:
            print(f"清理文件时出错: {e}")

        return {
            "message": "知识图谱更新成功",
            "kg_id": kg_id,
            "total_entities": len(merged_entities),
            "total_relations": len(merged_relations),
            "entities_added": len(temp_kg.entities),
            "relations_added": len(temp_kg.relations)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新图谱失败: {str(e)}")
