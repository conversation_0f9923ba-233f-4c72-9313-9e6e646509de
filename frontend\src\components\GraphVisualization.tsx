import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { Card, Spin, message, Button, Space, Tooltip, Alert, Progress, Badge } from 'antd'
import { ZoomInOutlined, ZoomOutOutlined, AimOutlined, ExpandOutlined, WarningOutlined, DashboardOutlined } from '@ant-design/icons'
import * as d3 from 'd3'
import type { GraphNode, GraphEdge } from '../types'
import { getAllNodeTags, filterNodesByTags } from '../utils/tagUtils'
import { useProgressiveRender } from '../hooks/useProgressiveRender'
import { usePerformanceMonitor } from '../hooks/usePerformanceMonitor'
import { useLevelOfDetail } from '../hooks/useLevelOfDetail'

interface GraphVisualizationProps {
  nodes: GraphNode[]
  edges: GraphEdge[]
  onNodeClick?: (node: GraphNode) => void
  onEdgeClick?: (edge: GraphEdge) => void
  width?: number
  height?: number
  loading?: boolean
  searchResults?: GraphNode[]
  selectedTags?: string[]
  onTagFilter?: (tags: string[]) => void
}

// 性能配置常量
const PERFORMANCE_CONFIG = {
  MAX_NODES_FULL_RENDER: 500,      // 完整渲染的最大节点数
  MAX_NODES_SIMPLIFIED: 1000,     // 简化渲染的最大节点数
  MAX_EDGES_SHOW_LABELS: 100,     // 显示边标签的最大边数
  MAX_NODES_SHOW_LABELS: 300,     // 显示节点标签的最大节点数
  TICK_THROTTLE_MS: 16,           // tick事件节流间隔(60fps)
  LARGE_GRAPH_WARNING: 800,       // 大图谱警告阈值
}

const GraphVisualization: React.FC<GraphVisualizationProps> = ({
  nodes,
  edges,
  onNodeClick,
  onEdgeClick,
  width = 800,
  height = 600,
  loading = false,
  searchResults = [],
  selectedTags = [],
  onTagFilter
}) => {
  const svgRef = useRef<SVGSVGElement>(null)
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [zoomTransform, setZoomTransform] = useState<d3.ZoomTransform | null>(null)
  const [renderMode, setRenderMode] = useState<'full' | 'simplified' | 'minimal'>('full')
  const [showPerformanceWarning, setShowPerformanceWarning] = useState(false)
  const [showPerformancePanel, setShowPerformancePanel] = useState(false)
  const simulationRef = useRef<d3.Simulation<any, any> | null>(null)
  const zoomRef = useRef<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null)
  const lastTickTime = useRef<number>(0)

  // 过滤节点和边
  const filteredNodes = React.useMemo(() => {
    return filterNodesByTags(nodes, selectedTags)
  }, [nodes, selectedTags])

  const filteredEdges = React.useMemo(() => {
    if (selectedTags.length === 0) return edges
    const filteredNodeIds = new Set(filteredNodes.map(n => n.id))
    return edges.filter(edge =>
      filteredNodeIds.has(edge.source) && filteredNodeIds.has(edge.target)
    )
  }, [edges, filteredNodes])

  // 渐进式渲染
  const progressiveRender = useProgressiveRender(filteredNodes, filteredEdges, {
    batchSize: renderMode === 'minimal' ? 100 : renderMode === 'simplified' ? 75 : 50,
    renderDelay: renderMode === 'minimal' ? 50 : 100,
    maxNodes: renderMode === 'minimal' ? 2000 : renderMode === 'simplified' ? 1500 : 1000
  })

  // 使用渐进式渲染的节点和边，或者在小图谱时直接使用原始数据
  const renderNodes = filteredNodes.length > PERFORMANCE_CONFIG.MAX_NODES_FULL_RENDER
    ? progressiveRender.visibleNodes
    : filteredNodes

  const renderEdges = filteredNodes.length > PERFORMANCE_CONFIG.MAX_NODES_FULL_RENDER
    ? progressiveRender.visibleEdges
    : filteredEdges

  // 性能监控
  const performanceMonitor = usePerformanceMonitor(renderNodes.length, renderEdges.length)

  // LOD (Level of Detail) 机制
  const lodSystem = useLevelOfDetail(renderNodes, renderEdges, zoomTransform)

  // 最终渲染的节点和边（结合渐进式渲染和LOD）
  const finalRenderNodes = lodSystem.visibleNodes
  const finalRenderEdges = lodSystem.visibleEdges

  // 性能分析和渲染模式决策
  const performanceAnalysis = useMemo(() => {
    const nodeCount = filteredNodes.length
    const edgeCount = filteredEdges.length
    const renderNodeCount = renderNodes.length
    const renderEdgeCount = renderEdges.length
    const finalNodeCount = finalRenderNodes.length
    const finalEdgeCount = finalRenderEdges.length

    let mode: 'full' | 'simplified' | 'minimal' = 'full'
    let warning = false

    if (nodeCount > PERFORMANCE_CONFIG.MAX_NODES_SIMPLIFIED) {
      mode = 'minimal'
      warning = true
    } else if (nodeCount > PERFORMANCE_CONFIG.MAX_NODES_FULL_RENDER) {
      mode = 'simplified'
      warning = nodeCount > PERFORMANCE_CONFIG.LARGE_GRAPH_WARNING
    }

    return {
      nodeCount,
      edgeCount,
      renderNodeCount,
      renderEdgeCount,
      finalNodeCount,
      finalEdgeCount,
      mode,
      warning,
      shouldShowLabels: lodSystem.currentLevel.showNodeLabels,
      shouldShowEdgeLabels: lodSystem.currentLevel.showEdgeLabels,
      isProgressive: nodeCount > PERFORMANCE_CONFIG.MAX_NODES_FULL_RENDER,
      lodLevel: lodSystem.currentLevel.name,
      lodDescription: lodSystem.description
    }
  }, [filteredNodes.length, filteredEdges.length, renderNodes.length, renderEdges.length,
      finalRenderNodes.length, finalRenderEdges.length, lodSystem.currentLevel, lodSystem.description])

  // 更新渲染模式和警告状态
  useEffect(() => {
    setRenderMode(performanceAnalysis.mode)
    setShowPerformanceWarning(performanceAnalysis.warning)
  }, [performanceAnalysis])

  // 缩放控制函数
  const handleZoomIn = useCallback(() => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current)
        .transition()
        .duration(300)
        .call(zoomRef.current.scaleBy, 1.5)
    }
  }, [])

  const handleZoomOut = useCallback(() => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current)
        .transition()
        .duration(300)
        .call(zoomRef.current.scaleBy, 1 / 1.5)
    }
  }, [])

  const handleResetZoom = useCallback(() => {
    if (svgRef.current && zoomRef.current) {
      d3.select(svgRef.current)
        .transition()
        .duration(500)
        .call(zoomRef.current.transform, d3.zoomIdentity)
    }
  }, [])

  const handleFitToView = useCallback(() => {
    if (svgRef.current && nodes.length > 0) {
      const svg = d3.select(svgRef.current)
      const bounds = svg.select('.nodes').node()?.getBBox()

      if (bounds && zoomRef.current) {
        const fullWidth = width
        const fullHeight = height
        const widthScale = fullWidth / bounds.width
        const heightScale = fullHeight / bounds.height
        const scale = Math.min(widthScale, heightScale) * 0.8

        const translate = [
          fullWidth / 2 - scale * (bounds.x + bounds.width / 2),
          fullHeight / 2 - scale * (bounds.y + bounds.height / 2)
        ]

        svg.transition()
          .duration(750)
          .call(zoomRef.current.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale))
      }
    }
  }, [nodes, width, height])

  // 聚焦到搜索结果
  const focusOnSearchResults = useCallback(() => {
    if (searchResults.length > 0 && svgRef.current && zoomRef.current) {
      const svg = d3.select(svgRef.current)
      const searchNodeIds = new Set(searchResults.map(n => n.id))

      // 高亮搜索结果节点
      svg.selectAll('circle')
        .style('stroke', (d: any) => searchNodeIds.has(d.id) ? '#ff4d4f' : '#fff')
        .style('stroke-width', (d: any) => searchNodeIds.has(d.id) ? 4 : 2)

      // 计算搜索结果的边界
      const searchNodes = nodes.filter(n => searchNodeIds.has(n.id))
      if (searchNodes.length > 0) {
        const nodeElements = svg.selectAll('circle').nodes() as SVGCircleElement[]
        const searchElements = nodeElements.filter((_, i) => searchNodeIds.has(nodes[i].id))

        if (searchElements.length > 0) {
          const bounds = {
            x: Math.min(...searchElements.map(el => +el.getAttribute('cx')!)),
            y: Math.min(...searchElements.map(el => +el.getAttribute('cy')!)),
            x2: Math.max(...searchElements.map(el => +el.getAttribute('cx')!)),
            y2: Math.max(...searchElements.map(el => +el.getAttribute('cy')!))
          }

          const centerX = (bounds.x + bounds.x2) / 2
          const centerY = (bounds.y + bounds.y2) / 2
          const scale = Math.min(width / (bounds.x2 - bounds.x + 200), height / (bounds.y2 - bounds.y + 200), 2)

          svg.transition()
            .duration(750)
            .call(zoomRef.current.transform,
              d3.zoomIdentity
                .translate(width / 2, height / 2)
                .scale(scale)
                .translate(-centerX, -centerY)
            )
        }
      }
    }
  }, [searchResults, nodes, width, height])

  useEffect(() => {
    if (searchResults.length > 0) {
      focusOnSearchResults()
    }
  }, [searchResults, focusOnSearchResults])

  useEffect(() => {
    if (!svgRef.current || loading || nodes.length === 0) return

    try {
      // 开始性能监控
      performanceMonitor.startRenderTiming()

      // 清除之前的内容
      d3.select(svgRef.current).selectAll('*').remove()

    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', height)

    // 创建容器组
    const container = svg.append('g')

    // 创建缩放行为
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 10])

    zoomRef.current = zoom
    svg.call(zoom)

    // 根据图谱大小和渲染模式优化力导向参数
    const getForceParameters = () => {
      const nodeCount = finalRenderNodes.length
      const edgeCount = finalRenderEdges.length
      const density = edgeCount / Math.max(1, nodeCount * (nodeCount - 1) / 2)

      // 基础距离根据节点密度和LOD级别动态调整
      const baseDensityFactor = Math.max(1, 1 + density * 5)
      const lodDistanceMultiplier = lodSystem.currentLevel.name === 'minimal' ? 0.8 :
                                   lodSystem.currentLevel.name === 'reduced' ? 0.9 :
                                   lodSystem.currentLevel.name === 'normal' ? 1.0 : 1.2
      const baseDistance = (renderMode === 'minimal' ? 120 :
                           renderMode === 'simplified' ? 180 : 220) * baseDensityFactor * lodDistanceMultiplier

      // 节点数量越多，距离增加越明显
      const nodeCountFactor = Math.sqrt(nodeCount / 100)
      const adaptiveDistance = baseDistance * Math.max(1, nodeCountFactor)

      return {
        linkDistance: Math.max(adaptiveDistance, 100 + nodeCount / 2),
        linkStrength: Math.max(0.01, 0.08 - nodeCount / 15000),
        chargeStrength: Math.max(-2000, -400 - nodeCount * 3),
        chargeDistanceMax: Math.min(1000, 500 + nodeCount),
        collisionRadius: lodSystem.currentLevel.nodeRadius * Math.max(1, nodeCountFactor * 0.5),
        alphaDecay: nodeCount > 300 ? 0.025 : 0.015,
        velocityDecay: nodeCount > 300 ? 0.7 : 0.5,
        // 添加额外的分离力，防止节点聚集
        separationStrength: Math.min(-50, -10 - nodeCount / 10)
      }
    }

    const forceParams = getForceParameters()

    // 创建优化的力导向图模拟
    const simulation = d3.forceSimulation(finalRenderNodes as any)
      .force('link', d3.forceLink(finalRenderEdges).id((d: any) => d.id)
        .distance(forceParams.linkDistance)
        .strength(forceParams.linkStrength))
      .force('charge', d3.forceManyBody()
        .strength(forceParams.chargeStrength)
        .distanceMax(forceParams.chargeDistanceMax))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide()
        .radius(forceParams.collisionRadius)
        .strength(0.8)) // 增强碰撞检测
      .force('separation', d3.forceManyBody()
        .strength(forceParams.separationStrength)
        .distanceMin(forceParams.collisionRadius * 2)
        .distanceMax(forceParams.collisionRadius * 4)) // 额外的分离力
      .alphaDecay(forceParams.alphaDecay)
      .velocityDecay(forceParams.velocityDecay)

    simulationRef.current = simulation

    // 创建箭头标记
    const defs = container.append('defs')
    defs.append('marker')
      .attr('id', 'arrowhead')
      .attr('viewBox', '-0 -5 10 10')
      .attr('refX', 25)
      .attr('refY', 0)
      .attr('orient', 'auto')
      .attr('markerWidth', 8)
      .attr('markerHeight', 8)
      .attr('xoverflow', 'visible')
      .append('svg:path')
      .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
      .attr('fill', '#999')
      .style('stroke', 'none')

    // 根据渲染模式绘制边
    const link = container.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(finalRenderEdges)
      .enter().append('line')
      .attr('stroke', '#999')
      .attr('stroke-opacity', renderMode === 'minimal' ? 0.3 : 0.6)
      .attr('stroke-width', lodSystem.currentLevel.edgeWidth)
      .attr('marker-end', renderMode !== 'minimal' ? 'url(#arrowhead)' : null)
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation()
        onEdgeClick?.(d)
      })

    // 只在非最小模式下添加悬停效果
    if (renderMode !== 'minimal') {
      link
        .on('mouseover', function(event, d) {
          d3.select(this).attr('stroke-width', 4)
        })
        .on('mouseout', function(event, d) {
          d3.select(this).attr('stroke-width', 2)
        })
    }

    // 根据性能分析决定是否绘制边标签
    const linkLabel = performanceAnalysis.shouldShowEdgeLabels && renderMode !== 'minimal'
      ? container.append('g')
          .attr('class', 'link-labels')
          .selectAll('text')
          .data(finalRenderEdges)
          .enter().append('text')
          .attr('text-anchor', 'middle')
          .attr('font-size', renderMode === 'simplified' ? '9px' : '10px')
          .attr('fill', '#666')
          .style('opacity', 0)
          .style('pointer-events', 'none')
          .text(d => d.relation_type)
      : container.append('g').attr('class', 'link-labels') // 空组，用于后续引用

    // 根据渲染模式绘制节点
    const getNodeRadius = () => {
      const baseRadius = renderMode === 'minimal' ? 15 :
                        renderMode === 'simplified' ? 20 : 25
      const scaleFactor = Math.max(0.5, 1 - renderNodes.length / 2000)
      return Math.max(8, baseRadius * scaleFactor)
    }

    const node = container.append('g')
      .attr('class', 'nodes')
      .selectAll('circle')
      .data(finalRenderNodes)
      .enter().append('circle')
      .attr('r', lodSystem.currentLevel.nodeRadius)
      .attr('fill', (d) => getNodeColor(d.type))
      .attr('stroke', '#fff')
      .attr('stroke-width', renderMode === 'minimal' ? 1 : 2)
      .style('cursor', 'pointer')
      .style('transition', renderMode !== 'minimal' ? 'all 0.3s ease' : 'none')

    // 根据渲染模式添加交互功能
    if (renderMode !== 'minimal') {
      node.call(d3.drag<SVGCircleElement, GraphNode>()
        .on('start', (event, d: any) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
        })
        .on('drag', (event, d: any) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on('end', (event, d: any) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
        })
      )
    }

    node.on('click', (event, d) => {
      event.stopPropagation()
      setSelectedNode(d.id)
      onNodeClick?.(d)
    })

    // 只在完整渲染模式下添加悬停效果
    if (renderMode === 'full') {
      const baseRadius = getNodeRadius()
      node
        .on('mouseover', function(event, d) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr('r', baseRadius * 1.3)
            .attr('stroke-width', 3)
        })
        .on('mouseout', function(event, d) {
          if (d.id !== selectedNode) {
            d3.select(this)
              .transition()
              .duration(200)
              .attr('r', baseRadius)
              .attr('stroke-width', 2)
          }
        })
    }
    // 根据性能分析决定是否绘制节点标签
    const nodeLabel = performanceAnalysis.shouldShowLabels && renderMode !== 'minimal'
      ? container.append('g')
          .attr('class', 'node-labels')
          .selectAll('text')
          .data(finalRenderNodes)
          .enter().append('text')
          .attr('text-anchor', 'middle')
          .attr('dy', '.35em')
          .attr('font-size', () => {
            const baseSize = renderMode === 'simplified' ? 10 : 12
            const scaleFactor = Math.max(0.7, 1 - filteredNodes.length / 1000)
            return Math.max(8, baseSize * scaleFactor) + 'px'
          })
          .attr('font-weight', 'bold')
          .attr('fill', '#333')
          .text(d => {
            const maxLength = renderMode === 'simplified' ? 6 :
                             renderNodes.length > 200 ? 8 : 12
            return d.name.length > maxLength ? d.name.substring(0, maxLength) + '...' : d.name
          })
          .style('pointer-events', 'none')
          .style('opacity', 0) // 初始隐藏，通过缩放控制显示
      : container.append('g').attr('class', 'node-labels') // 空组，用于后续引用

    // 只在LOD级别允许且节点数量较少时绘制属性标签
    const nodeTagsGroup = lodSystem.currentLevel.showNodeProperties && finalRenderNodes.length <= 100
      ? container.append('g')
          .attr('class', 'node-tags')
          .selectAll('g')
          .data(finalRenderNodes)
          .enter().append('g')
          .attr('class', 'node-tag-group')
      : container.append('g').attr('class', 'node-tags') // 空组

    if (lodSystem.currentLevel.showNodeProperties && finalRenderNodes.length <= 100) {
      nodeTagsGroup.each(function(d) {
        const group = d3.select(this)
        const tags = getAllNodeTags(d)
      const radius = Math.max(20, Math.min(30, 25 - filteredNodes.length / 100))

      tags.slice(0, 3).forEach((tag, i) => { // 最多显示3个标签
        const tagText = group.append('text')
          .attr('text-anchor', 'middle')
          .attr('dy', radius + 15 + i * 12) // 在节点下方显示
          .attr('font-size', '9px')
          .attr('fill', '#666')
          .attr('opacity', 0.8)
          .text(tag.length > 10 ? tag.substring(0, 10) + '...' : tag)
          .style('pointer-events', 'none')
      })
    })

    // 智能的标签可见性控制
    const updateLabelsVisibility = (transform: d3.ZoomTransform) => {
      const scale = transform.k

      // 根据渲染模式和性能分析调整标签显示阈值
      const nodeLabelThreshold = renderMode === 'simplified' ? 0.8 : 0.5
      const edgeLabelThreshold = renderMode === 'simplified' ? 2.0 : 1.5
      const tagLabelThreshold = 1.2

      // 节点标签显示控制
      if (performanceAnalysis.shouldShowLabels && renderMode !== 'minimal') {
        nodeLabel.style('opacity', scale > nodeLabelThreshold ? 1 : 0)
      }

      // 边标签显示控制
      if (performanceAnalysis.shouldShowEdgeLabels && renderMode !== 'minimal') {
        linkLabel.style('opacity', scale > edgeLabelThreshold ? 1 : 0)
      }

      // 属性标签显示控制（仅完整模式）
      if (renderMode === 'full' && filteredNodes.length <= 100) {
        nodeTagsGroup.style('opacity', scale > tagLabelThreshold ? 1 : 0)
      }
    }

    // 优化的tick事件处理，添加节流机制
    simulation.on('tick', () => {
      const now = performance.now()
      if (now - lastTickTime.current < PERFORMANCE_CONFIG.TICK_THROTTLE_MS) {
        return // 跳过此次更新，实现节流
      }
      lastTickTime.current = now

      // 更新帧率监控
      performanceMonitor.updateFrameRate()

      // 更新边位置
      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y)

      // 只在需要时更新边标签
      if (performanceAnalysis.shouldShowEdgeLabels && renderMode !== 'minimal') {
        linkLabel
          .attr('x', (d: any) => (d.source.x + d.target.x) / 2)
          .attr('y', (d: any) => (d.source.y + d.target.y) / 2)
      }

      // 更新节点位置
      node
        .attr('cx', (d: any) => d.x)
        .attr('cy', (d: any) => d.y)

      // 只在需要时更新节点标签
      if (performanceAnalysis.shouldShowLabels && renderMode !== 'minimal') {
        nodeLabel
          .attr('x', (d: any) => d.x)
          .attr('y', (d: any) => d.y)
      }

      // 只在完整模式下更新属性标签
      if (renderMode === 'full' && renderNodes.length <= 100) {
        nodeTagsGroup
          .attr('transform', (d: any) => `translate(${d.x}, ${d.y})`)
      }
    })

    // 设置缩放事件监听器
    zoom.on('zoom', (event) => {
      container.attr('transform', event.transform)
      setZoomTransform(event.transform)
      updateLabelsVisibility(event.transform)
    })

      // 初始化标签显示
      updateLabelsVisibility(d3.zoomIdentity)

      // 结束性能监控
      const renderTime = performanceMonitor.endRenderTiming()
      console.log(`图谱渲染完成，耗时: ${renderTime.toFixed(2)}ms`)

      // 清理函数
      return () => {
        simulation.stop()
      }
    } catch (error) {
      console.error('GraphVisualization 渲染错误:', error)
      message.error('图谱渲染失败，请刷新页面重试')

      // 记录错误到性能监控
      performanceMonitor.endRenderTiming()
    }
  }, [finalRenderNodes, finalRenderEdges, width, height, loading, onNodeClick, onEdgeClick, selectedNode, renderMode, performanceAnalysis, lodSystem.currentLevel])

  // 根据节点类型获取颜色
  const getNodeColor = (type: string): string => {
    const colorMap: Record<string, string> = {
      '人物': '#ff7875',
      '地点': '#73d13d',
      '组织': '#40a9ff',
      '概念': '#b37feb',
      '事件': '#ffb347',
      '产品': '#36cfc9',
      '时间': '#ffc069',
      'unknown': '#d9d9d9'
    }
    return colorMap[type] || colorMap['unknown']
  }

  if (loading) {
    return (
      <Card className="graph-container">
        <div className="loading-container">
          <Spin size="large" tip="加载图谱中..." />
        </div>
      </Card>
    )
  }

  if (nodes.length === 0) {
    return (
      <Card className="graph-container">
        <div className="loading-container">
          <div style={{ textAlign: 'center', color: '#999' }}>
            暂无图谱数据
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card
      className="graph-container"
      style={{ position: 'relative' }}
      bodyStyle={{ padding: 0 }}
    >
      {/* 性能警告 */}
      {showPerformanceWarning && (
        <Alert
          message="大型图谱性能提示"
          description={`当前图谱包含 ${performanceAnalysis.nodeCount} 个节点，已启用${renderMode === 'minimal' ? '最小化' : '简化'}渲染模式以提高性能。部分交互功能可能受限。`}
          type="warning"
          icon={<WarningOutlined />}
          showIcon
          closable
          onClose={() => setShowPerformanceWarning(false)}
          style={{
            position: 'absolute',
            top: 10,
            left: 10,
            right: 10,
            zIndex: 1000,
            marginBottom: 10
          }}
        />
      )}

      {/* 渐进式渲染进度 */}
      {performanceAnalysis.isProgressive && progressiveRender.isRendering && (
        <div style={{
          position: 'absolute',
          top: showPerformanceWarning ? 80 : 10,
          left: 10,
          right: 10,
          zIndex: 1000,
          background: 'rgba(255, 255, 255, 0.95)',
          padding: '8px 12px',
          borderRadius: '6px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{ marginBottom: 4, fontSize: '12px', color: '#666' }}>
            正在渲染图谱... ({progressiveRender.visibleNodes.length}/{performanceAnalysis.nodeCount} 节点)
          </div>
          <Progress
            percent={progressiveRender.progress}
            size="small"
            showInfo={false}
            strokeColor="#1890ff"
          />
        </div>
      )}

      {/* 渲染模式指示器 */}
      {renderMode !== 'full' && (
        <div style={{
          position: 'absolute',
          top: showPerformanceWarning ? 80 : 10,
          right: 10,
          zIndex: 1000,
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          {renderMode === 'minimal' ? '最小化渲染' : '简化渲染'}
        </div>
      )}

      {/* 控制按钮 */}
      <div style={{
        position: 'absolute',
        top: 16,
        right: 16,
        zIndex: 1000,
        background: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '6px',
        padding: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Space direction="vertical" size="small">
          <Tooltip title="放大">
            <Button
              type="text"
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              size="small"
            />
          </Tooltip>
          <Tooltip title="缩小">
            <Button
              type="text"
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              size="small"
            />
          </Tooltip>
          <Tooltip title="适应窗口">
            <Button
              type="text"
              icon={<ExpandOutlined />}
              onClick={handleFitToView}
              size="small"
            />
          </Tooltip>
          <Tooltip title="重置视图">
            <Button
              type="text"
              icon={<AimOutlined />}
              onClick={handleResetZoom}
              size="small"
            />
          </Tooltip>
          <Tooltip title="性能监控">
            <Badge
              dot={performanceMonitor.metrics.isLagging}
              status={performanceMonitor.getPerformanceLevel() === 'poor' ? 'error' :
                     performanceMonitor.getPerformanceLevel() === 'fair' ? 'warning' : 'success'}
            >
              <Button
                type="text"
                icon={<DashboardOutlined />}
                onClick={() => setShowPerformancePanel(!showPerformancePanel)}
                size="small"
              />
            </Badge>
          </Tooltip>
        </Space>
      </div>

      {/* 性能监控面板 */}
      {showPerformancePanel && (
        <div style={{
          position: 'absolute',
          top: 60,
          right: 16,
          width: 280,
          zIndex: 1000,
          background: 'rgba(255, 255, 255, 0.95)',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          fontSize: '12px'
        }}>
          <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#333' }}>
            性能监控
          </div>

          <div style={{ marginBottom: 6 }}>
            <span>渲染时间: </span>
            <span style={{ color: performanceMonitor.metrics.renderTime > 3000 ? '#ff4d4f' : '#52c41a' }}>
              {performanceMonitor.metrics.renderTime.toFixed(0)}ms
            </span>
          </div>

          <div style={{ marginBottom: 6 }}>
            <span>帧率: </span>
            <span style={{ color: performanceMonitor.metrics.frameRate < 30 ? '#ff4d4f' : '#52c41a' }}>
              {performanceMonitor.metrics.frameRate}fps
            </span>
          </div>

          <div style={{ marginBottom: 6 }}>
            <span>内存使用: </span>
            <span>{(performanceMonitor.metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</span>
          </div>

          <div style={{ marginBottom: 6 }}>
            <span>性能等级: </span>
            <Badge
              status={performanceMonitor.getPerformanceLevel() === 'poor' ? 'error' :
                     performanceMonitor.getPerformanceLevel() === 'fair' ? 'warning' : 'success'}
              text={performanceMonitor.getPerformanceLevel() === 'excellent' ? '优秀' :
                   performanceMonitor.getPerformanceLevel() === 'good' ? '良好' :
                   performanceMonitor.getPerformanceLevel() === 'fair' ? '一般' : '较差'}
            />
          </div>

          <div style={{ marginBottom: 6 }}>
            <span>LOD级别: </span>
            <Badge
              status={lodSystem.currentLevel.name === 'detailed' ? 'success' :
                     lodSystem.currentLevel.name === 'normal' ? 'processing' :
                     lodSystem.currentLevel.name === 'reduced' ? 'warning' : 'error'}
              text={lodSystem.description}
            />
          </div>

          {performanceMonitor.metrics.recommendations.length > 0 && (
            <div style={{ marginTop: 8, padding: 6, background: '#fff7e6', borderRadius: 4 }}>
              <div style={{ fontWeight: 'bold', marginBottom: 4, color: '#d46b08' }}>建议:</div>
              {performanceMonitor.metrics.recommendations.map((rec, index) => (
                <div key={index} style={{ fontSize: '11px', color: '#8c8c8c' }}>• {rec}</div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 图谱信息 */}
      <div style={{
        position: 'absolute',
        bottom: 16,
        left: 16,
        zIndex: 1000,
        background: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '6px',
        padding: '8px 12px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        fontSize: '12px',
        color: '#666'
      }}>
        节点: {finalRenderNodes.length}/{filteredNodes.length}/{nodes.length} | 边: {finalRenderEdges.length}/{filteredEdges.length}/{edges.length}
        {zoomTransform && (
          <span> | 缩放: {(zoomTransform.k * 100).toFixed(0)}%</span>
        )}
        {performanceAnalysis.isProgressive && (
          <span> | 渐进式渲染</span>
        )}
        <span> | {lodSystem.description}</span>
      </div>

      <svg
        ref={svgRef}
        style={{
          width: '100%',
          height: height,
          display: 'block'
        }}
      />
    </Card>
  )
}

export default GraphVisualization
