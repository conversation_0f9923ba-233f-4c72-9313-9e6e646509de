import { useState, useEffect, useRef, useCallback } from 'react'

interface PerformanceMetrics {
  renderTime: number
  frameRate: number
  memoryUsage: number
  nodeCount: number
  edgeCount: number
  isLagging: boolean
  recommendations: string[]
}

interface PerformanceMonitorConfig {
  sampleInterval: number
  lagThreshold: number
  memoryThreshold: number
}

const DEFAULT_CONFIG: PerformanceMonitorConfig = {
  sampleInterval: 1000, // 1秒采样一次
  lagThreshold: 30, // 低于30fps认为是卡顿
  memoryThreshold: 100 * 1024 * 1024 // 100MB内存阈值
}

export const usePerformanceMonitor = (
  nodeCount: number,
  edgeCount: number,
  config: Partial<PerformanceMonitorConfig> = {}
) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    frameRate: 60,
    memoryUsage: 0,
    nodeCount: 0,
    edgeCount: 0,
    isLagging: false,
    recommendations: []
  })

  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const renderStartTimeRef = useRef(0)
  const monitorIntervalRef = useRef<NodeJS.Timeout>()

  // 开始渲染计时
  const startRenderTiming = useCallback(() => {
    renderStartTimeRef.current = performance.now()
  }, [])

  // 结束渲染计时
  const endRenderTiming = useCallback(() => {
    const renderTime = performance.now() - renderStartTimeRef.current
    setMetrics(prev => ({ ...prev, renderTime }))
    return renderTime
  }, [])

  // 帧率监控
  const updateFrameRate = useCallback(() => {
    frameCountRef.current++
    const now = performance.now()
    const elapsed = now - lastTimeRef.current

    if (elapsed >= 1000) { // 每秒更新一次帧率
      const fps = Math.round((frameCountRef.current * 1000) / elapsed)
      frameCountRef.current = 0
      lastTimeRef.current = now

      setMetrics(prev => ({
        ...prev,
        frameRate: fps,
        isLagging: fps < finalConfig.lagThreshold
      }))
    }
  }, [finalConfig.lagThreshold])

  // 内存使用监控
  const updateMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const memoryUsage = memory.usedJSHeapSize || 0
      setMetrics(prev => ({ ...prev, memoryUsage }))
    }
  }, [])

  // 生成性能建议
  const generateRecommendations = useCallback((currentMetrics: PerformanceMetrics): string[] => {
    const recommendations: string[] = []

    if (currentMetrics.nodeCount > 1000) {
      recommendations.push('节点数量过多，建议启用简化渲染模式')
    }

    if (currentMetrics.frameRate < 30) {
      recommendations.push('帧率过低，建议减少显示的节点数量或关闭动画')
    }

    if (currentMetrics.renderTime > 5000) {
      recommendations.push('渲染时间过长，建议使用渐进式渲染')
    }

    if (currentMetrics.memoryUsage > finalConfig.memoryThreshold) {
      recommendations.push('内存使用过高，建议刷新页面或减少数据量')
    }

    if (currentMetrics.edgeCount > currentMetrics.nodeCount * 3) {
      recommendations.push('边的数量过多，建议隐藏部分边标签')
    }

    return recommendations
  }, [finalConfig.memoryThreshold])

  // 性能监控主循环
  useEffect(() => {
    const monitor = () => {
      updateMemoryUsage()
      
      setMetrics(prev => {
        const updated = {
          ...prev,
          nodeCount,
          edgeCount
        }
        updated.recommendations = generateRecommendations(updated)
        return updated
      })
    }

    monitorIntervalRef.current = setInterval(monitor, finalConfig.sampleInterval)
    
    return () => {
      if (monitorIntervalRef.current) {
        clearInterval(monitorIntervalRef.current)
      }
    }
  }, [nodeCount, edgeCount, finalConfig.sampleInterval, updateMemoryUsage, generateRecommendations])

  // 性能等级评估
  const getPerformanceLevel = useCallback((): 'excellent' | 'good' | 'fair' | 'poor' => {
    if (metrics.frameRate >= 50 && metrics.renderTime < 1000 && metrics.nodeCount < 500) {
      return 'excellent'
    } else if (metrics.frameRate >= 30 && metrics.renderTime < 3000 && metrics.nodeCount < 1000) {
      return 'good'
    } else if (metrics.frameRate >= 20 && metrics.renderTime < 5000 && metrics.nodeCount < 2000) {
      return 'fair'
    } else {
      return 'poor'
    }
  }, [metrics])

  // 是否需要降级渲染
  const shouldDegrade = useCallback((): boolean => {
    return metrics.isLagging || 
           metrics.renderTime > 5000 || 
           metrics.nodeCount > 1500 ||
           metrics.memoryUsage > finalConfig.memoryThreshold
  }, [metrics, finalConfig.memoryThreshold])

  return {
    metrics,
    startRenderTiming,
    endRenderTiming,
    updateFrameRate,
    getPerformanceLevel,
    shouldDegrade,
    isMonitoring: !!monitorIntervalRef.current
  }
}
