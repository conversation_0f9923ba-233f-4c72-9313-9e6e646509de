import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Card, Alert, Button, Space, Typography, Collapse } from 'antd'
import { ReloadOutlined, BugOutlined, WarningOutlined } from '@ant-design/icons'

const { Title, Paragraph, Text } = Typography
const { Panel } = Collapse

interface Props {
  children: ReactNode
  onRetry?: () => void
  fallbackMode?: 'minimal' | 'text' | 'hidden'
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  retryCount: number
}

class GraphErrorBoundary extends Component<Props, State> {
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('GraphVisualization 错误:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // 发送错误报告到监控系统
    this.reportError(error, errorInfo)
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 这里可以集成错误监控服务，如 Sentry
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    console.error('错误报告:', errorReport)
    
    // 可以发送到后端或第三方监控服务
    // fetch('/api/error-report', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorReport)
    // })
  }

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }))
      
      if (this.props.onRetry) {
        this.props.onRetry()
      }
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private getErrorSeverity = (): 'low' | 'medium' | 'high' => {
    const error = this.state.error
    if (!error) return 'low'

    // 根据错误类型判断严重程度
    if (error.message.includes('Memory') || error.message.includes('memory')) {
      return 'high'
    } else if (error.message.includes('Cannot read') || error.message.includes('undefined')) {
      return 'medium'
    } else {
      return 'low'
    }
  }

  private getSuggestions = (): string[] => {
    const severity = this.getErrorSeverity()
    const suggestions: string[] = []

    switch (severity) {
      case 'high':
        suggestions.push('尝试减少图谱数据量')
        suggestions.push('关闭浏览器其他标签页释放内存')
        suggestions.push('刷新页面重新开始')
        break
      case 'medium':
        suggestions.push('检查图谱数据格式是否正确')
        suggestions.push('尝试重新加载图谱')
        break
      case 'low':
        suggestions.push('点击重试按钮')
        suggestions.push('如果问题持续，请刷新页面')
        break
    }

    return suggestions
  }

  render() {
    if (this.state.hasError) {
      const severity = this.getErrorSeverity()
      const suggestions = this.getSuggestions()
      const canRetry = this.state.retryCount < this.maxRetries

      return (
        <Card className="graph-container">
          <div style={{ padding: '40px 20px', textAlign: 'center' }}>
            <Alert
              message="图谱渲染出现问题"
              description={
                <div>
                  <Paragraph>
                    <BugOutlined style={{ marginRight: 8, color: '#ff4d4f' }} />
                    图谱组件遇到了一个错误，无法正常显示。
                  </Paragraph>
                  
                  {severity === 'high' && (
                    <Alert
                      message="严重错误"
                      description="检测到内存相关问题，建议减少数据量或刷新页面"
                      type="error"
                      showIcon
                      style={{ marginBottom: 16 }}
                    />
                  )}

                  <div style={{ marginBottom: 16 }}>
                    <Title level={5}>建议解决方案：</Title>
                    <ul style={{ textAlign: 'left', paddingLeft: 20 }}>
                      {suggestions.map((suggestion, index) => (
                        <li key={index}>{suggestion}</li>
                      ))}
                    </ul>
                  </div>

                  <Space>
                    {canRetry && (
                      <Button 
                        type="primary" 
                        icon={<ReloadOutlined />}
                        onClick={this.handleRetry}
                      >
                        重试 ({this.maxRetries - this.state.retryCount} 次剩余)
                      </Button>
                    )}
                    <Button 
                      icon={<ReloadOutlined />}
                      onClick={this.handleReload}
                    >
                      刷新页面
                    </Button>
                  </Space>

                  <Collapse style={{ marginTop: 20, textAlign: 'left' }}>
                    <Panel header="错误详情 (开发者信息)" key="1">
                      <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                        <Text strong>错误信息:</Text>
                        <pre style={{ background: '#f5f5f5', padding: 8, marginTop: 8 }}>
                          {this.state.error?.message}
                        </pre>
                        
                        <Text strong>错误堆栈:</Text>
                        <pre style={{ background: '#f5f5f5', padding: 8, marginTop: 8, maxHeight: 200, overflow: 'auto' }}>
                          {this.state.error?.stack}
                        </pre>
                        
                        {this.state.errorInfo && (
                          <>
                            <Text strong>组件堆栈:</Text>
                            <pre style={{ background: '#f5f5f5', padding: 8, marginTop: 8, maxHeight: 200, overflow: 'auto' }}>
                              {this.state.errorInfo.componentStack}
                            </pre>
                          </>
                        )}
                      </div>
                    </Panel>
                  </Collapse>
                </div>
              }
              type="error"
              showIcon
            />
          </div>
        </Card>
      )
    }

    return this.props.children
  }
}

export default GraphErrorBoundary
