import { useState, useEffect, useCallback, useRef } from 'react'
import type { GraphNode, GraphEdge } from '../types'

interface ProgressiveRenderConfig {
  batchSize: number
  renderDelay: number
  maxNodes: number
}

interface ProgressiveRenderState {
  visibleNodes: GraphNode[]
  visibleEdges: GraphEdge[]
  isRendering: boolean
  progress: number
  renderComplete: boolean
}

const DEFAULT_CONFIG: ProgressiveRenderConfig = {
  batchSize: 50,
  renderDelay: 100,
  maxNodes: 1000
}

export const useProgressiveRender = (
  nodes: GraphNode[],
  edges: GraphEdge[],
  config: Partial<ProgressiveRenderConfig> = {}
): ProgressiveRenderState => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const [state, setState] = useState<ProgressiveRenderState>({
    visibleNodes: [],
    visibleEdges: [],
    isRendering: false,
    progress: 0,
    renderComplete: false
  })
  
  const renderTimeoutRef = useRef<NodeJS.Timeout>()
  const currentBatchRef = useRef(0)
  const abortControllerRef = useRef<AbortController>()

  // 计算节点重要性分数（用于优先渲染重要节点）
  const calculateNodeImportance = useCallback((node: GraphNode, edges: GraphEdge[]): number => {
    // 基于连接数的重要性
    const connections = edges.filter(e => e.source === node.id || e.target === node.id).length
    
    // 基于节点类型的重要性权重
    const typeWeights: Record<string, number> = {
      '人物': 1.2,
      '组织': 1.1,
      '地点': 1.0,
      '概念': 0.9,
      '事件': 0.8,
      '产品': 0.7,
      '时间': 0.6
    }
    
    const typeWeight = typeWeights[node.type] || 1.0
    
    // 基于名称长度的重要性（较短的名称通常更重要）
    const nameWeight = Math.max(0.5, 1 - node.name.length / 50)
    
    return connections * typeWeight * nameWeight
  }, [])

  // 渐进式渲染函数
  const startProgressiveRender = useCallback(() => {
    // 中止之前的渲染
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    abortControllerRef.current = new AbortController()
    const signal = abortControllerRef.current.signal
    
    // 如果节点数量较少，直接全部渲染
    if (nodes.length <= finalConfig.batchSize) {
      setState({
        visibleNodes: nodes,
        visibleEdges: edges,
        isRendering: false,
        progress: 100,
        renderComplete: true
      })
      return
    }

    // 如果节点数量超过最大限制，进行采样
    let nodesToRender = nodes
    if (nodes.length > finalConfig.maxNodes) {
      // 按重要性排序并取前N个
      const nodesWithImportance = nodes.map(node => ({
        node,
        importance: calculateNodeImportance(node, edges)
      }))
      
      nodesWithImportance.sort((a, b) => b.importance - a.importance)
      nodesToRender = nodesWithImportance
        .slice(0, finalConfig.maxNodes)
        .map(item => item.node)
    }

    setState(prev => ({
      ...prev,
      isRendering: true,
      progress: 0,
      renderComplete: false,
      visibleNodes: [],
      visibleEdges: []
    }))

    currentBatchRef.current = 0
    const totalBatches = Math.ceil(nodesToRender.length / finalConfig.batchSize)

    const renderNextBatch = () => {
      if (signal.aborted) return

      const startIndex = currentBatchRef.current * finalConfig.batchSize
      const endIndex = Math.min(startIndex + finalConfig.batchSize, nodesToRender.length)
      const batchNodes = nodesToRender.slice(0, endIndex)
      
      // 获取当前批次节点相关的边
      const nodeIds = new Set(batchNodes.map(n => n.id))
      const batchEdges = edges.filter(e => nodeIds.has(e.source) && nodeIds.has(e.target))
      
      const progress = Math.round((endIndex / nodesToRender.length) * 100)
      
      setState(prev => ({
        ...prev,
        visibleNodes: batchNodes,
        visibleEdges: batchEdges,
        progress,
        isRendering: endIndex < nodesToRender.length,
        renderComplete: endIndex >= nodesToRender.length
      }))

      currentBatchRef.current++

      if (endIndex < nodesToRender.length && !signal.aborted) {
        renderTimeoutRef.current = setTimeout(renderNextBatch, finalConfig.renderDelay)
      }
    }

    // 开始渲染
    renderNextBatch()
  }, [nodes, edges, finalConfig, calculateNodeImportance])

  // 当节点或边发生变化时，重新开始渐进式渲染
  useEffect(() => {
    startProgressiveRender()
    
    return () => {
      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [startProgressiveRender])

  return state
}
