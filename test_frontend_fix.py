#!/usr/bin/env python3
"""
测试前端文件对象修复的脚本
"""
import requests
import time

def test_frontend_fix():
    """测试前端文件对象修复"""
    
    # 获取图谱列表
    print("获取图谱列表...")
    response = requests.get("http://localhost:8000/api/kg/list")
    graphs = response.json()["graphs"]
    
    if not graphs:
        print("没有图谱可测试")
        return
    
    graph_id = graphs[0]["id"]
    graph_name = graphs[0]["name"]
    print(f"使用图谱: {graph_name} (ID: {graph_id})")
    
    # 创建测试文件
    test_content = """
    前端修复测试文档
    
    新增测试实体：
    - 前端工程师：负责用户界面开发
    - 后端工程师：负责服务器端逻辑
    - 全栈工程师：负责前后端开发
    
    新增测试关系：
    - 前端工程师 使用 React框架
    - 后端工程师 使用 Python语言
    - 全栈工程师 掌握 多种技术栈
    """
    
    # 创建临时文件
    with open("frontend_test.txt", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("开始测试图谱更新...")
    
    # 测试文件上传
    with open("frontend_test.txt", "rb") as f:
        files = {"file": ("frontend_test.txt", f, "text/plain")}
        
        try:
            start_time = time.time()
            response = requests.post(
                f"http://localhost:8000/api/kg/{graph_id}/update",
                files=files,
                timeout=300
            )
            end_time = time.time()
            
            print(f"请求耗时: {end_time - start_time:.2f} 秒")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 前端修复测试成功!")
                print(f"消息: {result.get('message', '')}")
                print(f"总实体数量: {result.get('total_entities', 0)}")
                print(f"总关系数量: {result.get('total_relations', 0)}")
                print(f"新增实体数量: {result.get('entities_added', 0)}")
                print(f"新增关系数量: {result.get('relations_added', 0)}")
            else:
                print(f"❌ 测试失败: {response.status_code}")
                print(f"错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    # 清理临时文件
    import os
    try:
        os.remove("frontend_test.txt")
        print("临时文件已清理")
    except:
        pass

if __name__ == "__main__":
    test_frontend_fix()
