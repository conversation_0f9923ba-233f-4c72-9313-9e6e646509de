# 知识图谱更新功能使用指南

## 功能概述

知识图谱更新功能允许用户向现有的知识图谱添加新文件，系统会自动提取新文件中的实体和关系，并与现有图谱进行智能合并，使图谱更加丰富和准确。

## 主要特性

### ✅ 智能合并
- **实体去重**: 自动识别和合并重复的实体
- **关系整合**: 智能处理新旧关系的合并
- **属性融合**: 合并实体的描述和属性信息

### ✅ 多格式支持
- TXT 文本文件
- PDF 文档
- DOCX Word文档
- DOC 旧版Word文档
- XLS/XLSX Excel表格
- CSV 数据文件

### ✅ 实时反馈
- 上传进度显示
- 处理状态更新
- 详细的更新结果统计

## 使用步骤

### 1. 选择要更新的图谱
1. 在知识图谱页面选择一个现有的图谱
2. 确保图谱已经加载并显示在可视化界面中

### 2. 启动更新流程
1. 点击左侧操作面板中的 **"添加文件"** 按钮
2. 系统会打开图谱更新对话框

### 3. 上传新文件
1. 在对话框中点击上传区域或拖拽文件
2. 选择要添加的文档文件
3. 系统会验证文件格式和大小（最大50MB）

### 4. 开始更新
1. 确认文件选择无误后，点击 **"开始更新"** 按钮
2. 系统开始处理文件并更新图谱
3. 可以看到实时的处理进度

### 5. 查看更新结果
1. 更新完成后，系统会显示详细的统计信息：
   - 新增实体数量
   - 新增关系数量
   - 总实体数量
   - 总关系数量
2. 图谱会自动刷新显示更新后的内容

## 更新机制说明

### 实体合并策略
- **名称匹配**: 基于实体名称进行初步匹配
- **语义相似度**: 使用AI模型计算语义相似度
- **属性融合**: 合并相同实体的不同属性和描述

### 关系处理策略
- **去重处理**: 移除完全重复的关系
- **关系增强**: 为现有实体添加新的关系
- **一致性检查**: 确保关系的逻辑一致性

### 质量保证
- **孤立节点检测**: 识别并尝试连接孤立的实体
- **关系推理**: 基于实体描述推理潜在关系
- **连通性分析**: 分析图谱的整体连通性

## 最佳实践

### 📋 文件准备建议
1. **相关性**: 选择与现有图谱主题相关的文档
2. **结构化**: 使用结构化的文档格式，便于信息提取
3. **完整性**: 确保文档内容完整，包含清晰的实体和关系描述

### 📋 更新策略
1. **渐进式更新**: 建议分批次添加文件，而不是一次性添加大量文件
2. **主题一致**: 保持添加文件的主题与原图谱一致
3. **质量优先**: 选择高质量、信息丰富的文档

### 📋 结果验证
1. **可视化检查**: 更新后检查图谱的可视化效果
2. **搜索测试**: 使用搜索功能验证新增实体是否正确
3. **关系验证**: 检查新增关系的合理性

## 注意事项

### ⚠️ 文件限制
- 单个文件大小不超过 50MB
- 支持的文件格式有限
- 建议使用UTF-8编码的文本文件

### ⚠️ 处理时间
- 处理时间取决于文件大小和复杂度
- 大文件可能需要几分钟的处理时间
- 请耐心等待，不要中途关闭页面

### ⚠️ 数据备份
- 更新前建议导出现有图谱作为备份
- 更新是不可逆的操作
- 如有问题可以联系管理员恢复

## 故障排除

### 常见问题
1. **文件上传失败**: 检查文件格式和大小
2. **处理超时**: 可能是文件过大或服务器繁忙
3. **更新结果异常**: 检查文件内容的质量和相关性

### 解决方案
1. **重新上传**: 尝试重新选择和上传文件
2. **分割文件**: 将大文件分割成小文件分批处理
3. **联系支持**: 如问题持续存在，请联系技术支持

## 示例场景

### 场景1: 团队信息更新
- **原图谱**: 包含公司基本组织架构
- **新文件**: 新员工入职信息文档
- **预期结果**: 图谱中增加新员工实体和相关关系

### 场景2: 项目信息扩充
- **原图谱**: 包含现有项目信息
- **新文件**: 新项目计划书
- **预期结果**: 图谱中增加新项目实体和项目关系

### 场景3: 技术栈更新
- **原图谱**: 包含当前技术架构
- **新文件**: 新技术调研报告
- **预期结果**: 图谱中增加新技术实体和技术关系

通过这个功能，您可以持续丰富和完善知识图谱，使其始终保持最新和最准确的信息。
