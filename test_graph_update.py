#!/usr/bin/env python3
"""
测试知识图谱更新功能的脚本
"""
import requests
import json
import time
from pathlib import Path

# 配置
BASE_URL = "http://localhost:8000/api"
TEST_FILE_PATH = "test_update_document.txt"

def test_graph_update():
    """测试图谱更新功能"""
    
    # 1. 首先获取现有的图谱列表
    print("1. 获取图谱列表...")
    response = requests.get(f"{BASE_URL}/kg/list")
    if response.status_code != 200:
        print(f"获取图谱列表失败: {response.status_code}")
        return
    
    graphs = response.json().get("graphs", [])
    if not graphs:
        print("没有找到现有图谱，请先创建一个图谱")
        return
    
    # 选择第一个图谱进行测试
    test_graph = graphs[0]
    graph_id = test_graph["id"]
    graph_name = test_graph["name"]
    
    print(f"选择测试图谱: {graph_name} (ID: {graph_id})")
    print(f"当前实体数量: {len(test_graph.get('entities', []))}")
    print(f"当前关系数量: {len(test_graph.get('relations', []))}")
    
    # 2. 检查测试文件是否存在
    test_file = Path(TEST_FILE_PATH)
    if not test_file.exists():
        print(f"测试文件不存在: {TEST_FILE_PATH}")
        return
    
    print(f"\n2. 准备上传文件: {TEST_FILE_PATH}")
    print(f"文件大小: {test_file.stat().st_size} bytes")
    
    # 3. 执行图谱更新
    print("\n3. 开始更新图谱...")
    
    with open(test_file, 'rb') as f:
        files = {'file': (test_file.name, f, 'text/plain')}
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{BASE_URL}/kg/{graph_id}/update",
                files=files,
                timeout=1800  # 30分钟超时
            )
            end_time = time.time()
        except requests.exceptions.Timeout:
            print("❌ 请求超时（30分钟），服务器可能正在处理大文件")
            return
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
            return
    
    print(f"请求耗时: {end_time - start_time:.2f} 秒")
    
    # 4. 检查结果
    if response.status_code == 200:
        result = response.json()
        print("\n✅ 图谱更新成功!")
        print(f"消息: {result.get('message', '')}")
        print(f"总实体数量: {result.get('total_entities', 0)}")
        print(f"总关系数量: {result.get('total_relations', 0)}")
        print(f"新增实体数量: {result.get('entities_added', 0)}")
        print(f"新增关系数量: {result.get('relations_added', 0)}")
        
        # 5. 验证更新后的图谱
        print("\n5. 验证更新后的图谱...")
        verify_response = requests.get(f"{BASE_URL}/kg/{graph_id}")
        if verify_response.status_code == 200:
            updated_graph = verify_response.json()
            print(f"验证成功 - 更新后实体数量: {len(updated_graph.get('entities', []))}")
            print(f"验证成功 - 更新后关系数量: {len(updated_graph.get('relations', []))}")
        else:
            print(f"验证失败: {verify_response.status_code}")
            
    else:
        print(f"\n❌ 图谱更新失败!")
        print(f"状态码: {response.status_code}")
        print(f"错误信息: {response.text}")

def test_file_validation():
    """测试文件验证功能"""
    print("\n=== 测试文件验证功能 ===")
    
    # 获取支持的文件格式
    response = requests.get(f"{BASE_URL}/files/supported-formats")
    if response.status_code == 200:
        formats = response.json()
        print("支持的文件格式:")
        for key, value in formats.items():
            print(f"  {key}: {value}")
    else:
        print("获取支持格式失败")

def main():
    """主函数"""
    print("=== 知识图谱更新功能测试 ===\n")
    
    try:
        # 测试文件验证
        test_file_validation()
        
        # 测试图谱更新
        test_graph_update()
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保服务器正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时，可能是文件太大或服务器处理时间过长")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
