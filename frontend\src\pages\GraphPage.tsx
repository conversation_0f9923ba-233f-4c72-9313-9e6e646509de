import React, { useState, useEffect } from 'react'
import {
  Row,
  Col,
  Card,
  Select,
  Input,
  Button,
  List,
  Typography,
  Space,
  Tag,
  Statistic,
  Drawer,
  Descriptions,
  message,
  Checkbox,
  Collapse
} from 'antd'
import {
  SearchOutlined,
  NodeIndexOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  PlusOutlined
} from '@ant-design/icons'
import { useQuery } from 'react-query'
import { knowledgeGraphApi } from '../services/api'
import GraphVisualization from '../components/GraphVisualization'
import GraphUpdateModal from '../components/GraphUpdateModal'
import ErrorBoundary from '../components/ErrorBoundary'
import type { Entity, Relation, GraphNode, GraphEdge, KnowledgeGraph } from '../types'
import { extractAllTags } from '../utils/tagUtils'

const { Title, Text } = Typography
const { Option } = Select
const { Search } = Input
const { Panel } = Collapse

const GraphPage: React.FC = () => {
  const [selectedGraphId, setSelectedGraphId] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null)
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [graphNodes, setGraphNodes] = useState<GraphNode[]>([])
  const [graphEdges, setGraphEdges] = useState<GraphEdge[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [updateModalVisible, setUpdateModalVisible] = useState(false)

  // 获取图谱列表
  const { data: graphList } = useQuery(
    'graphList',
    knowledgeGraphApi.getGraphList
  )

  // 获取选中的图谱
  const { data: currentGraph, isLoading: graphLoading } = useQuery(
    ['graph', selectedGraphId],
    () => knowledgeGraphApi.getGraph(selectedGraphId),
    {
      enabled: !!selectedGraphId,
      onSuccess: (graph) => {
        // 转换为可视化格式
        const nodes: GraphNode[] = graph.entities.map(entity => ({
          id: entity.id,
          name: entity.name,
          type: entity.type,
          properties: entity.properties
        }))

        const edges: GraphEdge[] = graph.relations.map(relation => ({
          source: relation.source_entity,
          target: relation.target_entity,
          relation_type: relation.relation_type,
          properties: relation.properties
        }))

        setGraphNodes(nodes)
        setGraphEdges(edges)

        // 提取所有可用标签
        setAvailableTags(extractAllTags(nodes))
        setSelectedTags([]) // 重置选中的标签
      }
    }
  )

  // 获取图谱统计
  const { data: graphStats } = useQuery(
    ['graphStats', selectedGraphId],
    () => knowledgeGraphApi.getGraphStats(selectedGraphId),
    {
      enabled: !!selectedGraphId
    }
  )

  // 搜索实体
  const { data: searchResults, refetch: searchEntities } = useQuery(
    ['searchEntities', selectedGraphId, searchQuery],
    () => knowledgeGraphApi.searchEntities(selectedGraphId, searchQuery),
    {
      enabled: !!selectedGraphId && !!searchQuery,
    }
  )

  // 处理图谱选择
  const handleGraphSelect = (graphId: string) => {
    setSelectedGraphId(graphId)
    setSearchQuery('')
    setSelectedEntity(null)
    setSelectedTags([])
  }

  // 处理标签筛选
  const handleTagFilter = (tags: string[]) => {
    setSelectedTags(tags)
  }

  // 处理标签选择
  const handleTagChange = (tag: string, checked: boolean) => {
    if (checked) {
      setSelectedTags([...selectedTags, tag])
    } else {
      setSelectedTags(selectedTags.filter(t => t !== tag))
    }
  }

  // 处理节点点击
  const handleNodeClick = (node: GraphNode) => {
    if (currentGraph) {
      const entity = currentGraph.entities.find(e => e.id === node.id)
      if (entity) {
        setSelectedEntity(entity)
        setDrawerVisible(true)
      }
    }
  }

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value)
    if (value && selectedGraphId) {
      searchEntities()
    }
  }

  // 导出图谱
  const handleExport = async () => {
    if (!selectedGraphId) return
    
    try {
      const data = await knowledgeGraphApi.exportGraph(selectedGraphId)
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `knowledge_graph_${selectedGraphId}.json`
      a.click()
      URL.revokeObjectURL(url)
      message.success('图谱导出成功')
    } catch (error) {
      message.error('导出失败')
    }
  }

  // 处理图谱更新成功
  const handleUpdateSuccess = () => {
    setUpdateModalVisible(false)
    // 重新获取图谱数据
    if (selectedGraphId) {
      window.location.reload() // 简单的刷新方式，也可以使用 React Query 的 refetch
    }
  }

  return (
    <div>
      <Title level={2}>知识图谱可视化</Title>

      <Row gutter={16}>
        {/* 左侧控制面板 */}
        <Col span={6}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 图谱选择 */}
            <Card title="选择图谱" size="small">
              <Select
                style={{ width: '100%' }}
                placeholder="选择要查看的图谱"
                value={selectedGraphId}
                onChange={handleGraphSelect}
              >
                {graphList?.graphs?.map(graph => (
                  <Option key={graph.id} value={graph.id}>
                    {graph.name}
                  </Option>
                ))}
              </Select>
            </Card>

            {/* 搜索 */}
            {selectedGraphId && (
              <Card title="搜索实体" size="small">
                <Search
                  placeholder="输入实体名称"
                  onSearch={handleSearch}
                  enterButton={<SearchOutlined />}
                />
              </Card>
            )}

            {/* 图谱统计 */}
            {graphStats && (
              <Card title="图谱统计" size="small">
                <Row gutter={8}>
                  <Col span={12}>
                    <Statistic
                      title="实体"
                      value={graphStats.total_entities}
                      valueStyle={{ fontSize: 16 }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="关系"
                      value={graphStats.total_relations}
                      valueStyle={{ fontSize: 16 }}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">
                    平均度数: {graphStats.avg_degree.toFixed(1)}
                  </Text>
                </div>
              </Card>
            )}

            {/* 标签筛选 */}
            {availableTags.length > 0 && (
              <Card title="标签筛选" size="small">
                <Collapse size="small" ghost>
                  <Panel header={`已选择 ${selectedTags.length} 个标签`} key="1">
                    <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        {availableTags.map(tag => (
                          <Checkbox
                            key={tag}
                            checked={selectedTags.includes(tag)}
                            onChange={(e) => handleTagChange(tag, e.target.checked)}
                          >
                            <Text style={{ fontSize: '12px' }}>{tag}</Text>
                          </Checkbox>
                        ))}
                      </Space>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Button
                        size="small"
                        onClick={() => setSelectedTags([])}
                        disabled={selectedTags.length === 0}
                      >
                        清除筛选
                      </Button>
                    </div>
                  </Panel>
                </Collapse>
              </Card>
            )}

            {/* 实体类型 */}
            {graphStats && (
              <Card title="实体类型" size="small">
                <Space wrap>
                  {Object.entries(graphStats.entity_types).map(([type, count]) => (
                    <Tag key={type} color="blue">
                      {type}: {count}
                    </Tag>
                  ))}
                </Space>
              </Card>
            )}

            {/* 搜索结果 */}
            {searchResults && searchResults.entities.length > 0 && (
              <Card title="搜索结果" size="small">
                <List
                  size="small"
                  dataSource={searchResults.entities}
                  renderItem={(entity) => (
                    <List.Item
                      className="search-result-item"
                      onClick={() => {
                        setSelectedEntity(entity)
                        setDrawerVisible(true)
                      }}
                    >
                      <List.Item.Meta
                        title={entity.name}
                        description={
                          <Space>
                            <Tag color="blue">{entity.type}</Tag>
                            {entity.description && (
                              <Text type="secondary" ellipsis>
                                {entity.description}
                              </Text>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            )}

            {/* 操作按钮 */}
            {selectedGraphId && (
              <Card size="small">
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setUpdateModalVisible(true)}
                  >
                    添加文件
                  </Button>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                  >
                    导出
                  </Button>
                  <Button
                    icon={<InfoCircleOutlined />}
                    onClick={() => setDrawerVisible(true)}
                  >
                    详情
                  </Button>
                </Space>
              </Card>
            )}
          </Space>
        </Col>

        {/* 右侧图谱可视化 */}
        <Col span={18}>
          <ErrorBoundary>
            <GraphVisualization
              nodes={graphNodes}
              edges={graphEdges}
              onNodeClick={handleNodeClick}
              loading={graphLoading}
              width={800}
              height={600}
              searchResults={searchResults?.entities || []}
              selectedTags={selectedTags}
              onTagFilter={handleTagFilter}
            />
          </ErrorBoundary>
        </Col>
      </Row>

      {/* 实体详情抽屉 */}
      <Drawer
        title="实体详情"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={400}
      >
        {selectedEntity && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="名称">
                {selectedEntity.name}
              </Descriptions.Item>
              <Descriptions.Item label="类型">
                <Tag color="blue">{selectedEntity.type}</Tag>
              </Descriptions.Item>
              {selectedEntity.description && (
                <Descriptions.Item label="描述">
                  {selectedEntity.description}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="创建时间">
                {new Date(selectedEntity.created_at).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            {/* 属性 */}
            {Object.keys(selectedEntity.properties).length > 0 && (
              <Card title="属性" size="small">
                <Descriptions column={1} size="small">
                  {Object.entries(selectedEntity.properties).map(([key, value]) => (
                    <Descriptions.Item key={key} label={key}>
                      {String(value)}
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              </Card>
            )}

            {/* 相关关系 */}
            {currentGraph && (
              <Card title="相关关系" size="small">
                <List
                  size="small"
                  dataSource={currentGraph.relations.filter(
                    r => r.source_entity === selectedEntity.id || 
                         r.target_entity === selectedEntity.id
                  )}
                  renderItem={(relation) => {
                    const isSource = relation.source_entity === selectedEntity.id
                    const otherEntityId = isSource ? relation.target_entity : relation.source_entity
                    const otherEntity = currentGraph.entities.find(e => e.id === otherEntityId)
                    
                    return (
                      <List.Item>
                        <Space>
                          <Tag color="green">{relation.relation_type}</Tag>
                          <Text>
                            {isSource ? '→' : '←'} {otherEntity?.name}
                          </Text>
                        </Space>
                      </List.Item>
                    )
                  }}
                />
              </Card>
            )}
          </Space>
        )}
      </Drawer>

      {/* 图谱更新模态框 */}
      <GraphUpdateModal
        visible={updateModalVisible}
        onCancel={() => setUpdateModalVisible(false)}
        onSuccess={handleUpdateSuccess}
        graphId={selectedGraphId}
        graphName={graphList?.graphs?.find(g => g.id === selectedGraphId)?.name || ''}
      />
    </div>
  )
}

export default GraphPage
