#!/usr/bin/env python3
"""
简单的图谱更新测试
"""
import requests
import time

def test_update():
    # 获取图谱列表
    print("获取图谱列表...")
    response = requests.get("http://localhost:8000/api/kg/list")
    graphs = response.json()["graphs"]
    
    if not graphs:
        print("没有图谱可测试")
        return
    
    graph_id = graphs[0]["id"]
    print(f"使用图谱ID: {graph_id}")
    
    # 准备测试文件
    test_content = """
    测试更新文档
    
    新增人员：
    - 测试员工A：负责质量保证
    - 测试员工B：负责自动化测试
    
    新增关系：
    - 测试员工A 属于 质量保证部门
    - 测试员工B 使用 自动化测试工具
    """
    
    # 创建临时文件
    with open("temp_test.txt", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    # 上传文件
    print("开始更新图谱...")
    with open("temp_test.txt", "rb") as f:
        files = {"file": ("temp_test.txt", f, "text/plain")}
        
        try:
            response = requests.post(
                f"http://localhost:8000/api/kg/{graph_id}/update",
                files=files,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 更新成功!")
                print(f"结果: {result}")
            else:
                print(f"❌ 更新失败: {response.status_code}")
                print(f"错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
    
    # 清理临时文件
    import os
    try:
        os.remove("temp_test.txt")
    except:
        pass

if __name__ == "__main__":
    test_update()
