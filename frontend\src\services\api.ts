import axios from 'axios'
import type {
  KnowledgeGraph,
  GraphSearchResult,
  GraphStats,
  FileInfo,
  ProcessResult,
  UploadResponse,
  FolderScanResult,
  GraphListItem
} from '../types'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 3 * 60 * 60 * 1000, // 3小时超时
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// 文件相关API
export const fileApi = {
  // 上传文件
  uploadFile: (file: File): Promise<UploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 3 * 60 * 60 * 1000, // 3小时超时
    })
  },

  // 处理文件
  processFile: (fileId: string): Promise<ProcessResult> => {
    return api.post(`/files/process/${fileId}`, {}, {
      timeout: 3 * 60 * 60 * 1000, // 3小时超时
    })
  },

  // 扫描文件夹
  scanFolder: (folderPath: string): Promise<FolderScanResult> => {
    const formData = new FormData()
    formData.append('folder_path', folderPath)
    return api.post('/files/scan-folder', formData)
  },

  // 批量处理文件
  batchProcessFiles: (fileIds: string[]): Promise<ProcessResult[]> => {
    return api.post('/files/batch-process', { file_ids: fileIds }, {
      timeout: 3 * 60 * 60 * 1000, // 3小时超时
    })
  },

  // 批量处理文件并整合为单一知识图谱
  batchProcessFilesIntegrated: (fileIds: string[]): Promise<ProcessResult> => {
    return api.post('/files/batch-process-integrated', { file_ids: fileIds }, {
      timeout: 3 * 60 * 60 * 1000, // 3小时超时
    })
  },

  // 获取支持的文件格式
  getSupportedFormats: () => {
    return api.get('/files/supported-formats')
  },
}

// 知识图谱相关API
export const knowledgeGraphApi = {
  // 获取图谱列表
  getGraphList: (): Promise<{ graphs: GraphListItem[] }> => {
    return api.get('/kg/list')
  },

  // 获取指定图谱
  getGraph: (kgId: string): Promise<KnowledgeGraph> => {
    return api.get(`/kg/${kgId}`)
  },

  // 获取图谱统计信息
  getGraphStats: (kgId: string): Promise<GraphStats> => {
    return api.get(`/kg/${kgId}/stats`)
  },

  // 搜索实体
  searchEntities: (
    kgId: string,
    query: string,
    limit: number = 10
  ): Promise<GraphSearchResult> => {
    return api.get(`/kg/${kgId}/search`, {
      params: { query, limit },
    })
  },

  // 获取实体邻居
  getEntityNeighbors: (
    kgId: string,
    entityId: string,
    depth: number = 1
  ): Promise<GraphSearchResult> => {
    return api.get(`/kg/${kgId}/entity/${entityId}/neighbors`, {
      params: { depth },
    })
  },

  // 导出图谱
  exportGraph: (kgId: string, format: string = 'json') => {
    return api.get(`/kg/${kgId}/export`, {
      params: { format },
    })
  },

  // 删除图谱
  deleteGraph: (kgId: string) => {
    return api.delete(`/kg/${kgId}`)
  },

  // 合并图谱
  mergeGraphs: (kgId: string, otherKgId: string) => {
    return api.post(`/kg/${kgId}/merge/${otherKgId}`)
  },

  // 更新图谱（添加新文件）
  updateGraphWithFile: (kgId: string, file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/kg/${kgId}/update`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 3 * 60 * 60 * 1000, // 3小时超时，专门用于图谱更新
    })
  },
}

// LLM相关API
export const llmApi = {
  // 分析文本
  analyzeText: (text: string, analysisType: string = 'entities_relations') => {
    return api.post('/llm/analyze', {
      text,
      analysis_type: analysisType,
    })
  },

  // 实体分类
  classifyEntity: (entityName: string, context: string = '') => {
    return api.post('/llm/classify-entity', {
      entity_name: entityName,
      context,
    })
  },

  // 获取可用模型
  getAvailableModels: () => {
    return api.get('/llm/models')
  },

  // 获取LLM配置
  getLLMConfig: () => {
    return api.get('/llm/config')
  },

  // 测试连接
  testConnection: () => {
    return api.post('/llm/test-connection')
  },
}

export default api
